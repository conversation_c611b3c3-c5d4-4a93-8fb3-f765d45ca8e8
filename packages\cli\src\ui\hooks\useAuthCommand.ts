/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { useState, useCallback, useEffect } from 'react';
import { LoadedSettings, SettingScope } from '../../config/settings.js';
import {
  AuthType,
  Config,
  clearCachedCredentialFile,
  getErrorMessage,
} from '@google/gemini-cli-core';
import { runExitCleanup } from '../../utils/cleanup.js';
import {
  suggestBestProvider,
  getAuthTypeFromProvider,
  validateAuthMethod
} from '../../config/auth.js';

export const useAuthCommand = (
  settings: LoadedSettings,
  setAuthError: (error: string | null) => void,
  config: Config,
) => {
  const [isAuthDialogOpen, setIsAuthDialogOpen] = useState(false);
  const [isInitialSetup, setIsInitialSetup] = useState(false);

  const openAuthDialog = useCallback(() => {
    setIsAuthDialogOpen(true);
  }, []);

  const [isAuthenticating, setIsAuthenticating] = useState(false);

  useEffect(() => {
    const initializeAuth = async () => {
      const authType = settings.merged.selectedAuthType;

      // Check if this is the first time setup
      if (!authType) {
        // Try to suggest a provider based on .env configuration
        const suggestedProvider = await suggestBestProvider();
        if (suggestedProvider) {
          const suggestedAuthType = getAuthTypeFromProvider(suggestedProvider);
          if (suggestedAuthType) {
            const validationError = validateAuthMethod(suggestedAuthType);
            if (!validationError) {
              // Auto-configure the suggested provider
              settings.setValue(SettingScope.User, 'selectedAuthType', suggestedAuthType);
              console.log(`Auto-configured authentication for ${suggestedProvider} provider based on .env settings.`);
              return;
            }
          }
        }

        // No valid provider found, show dialog
        setIsAuthDialogOpen(true);
        setIsInitialSetup(true);
        return;
      }

      // Existing auth type, try to authenticate
      if (isAuthDialogOpen) {
        return;
      }

      try {
        setIsAuthenticating(true);
        await config.refreshAuth(authType);
        console.log(`Authenticated via "${authType}".`);
      } catch (e) {
        setAuthError(`Failed to login. Message: ${getErrorMessage(e)}`);
        openAuthDialog();
      } finally {
        setIsAuthenticating(false);
      }
    };

    void initializeAuth();
  }, [isAuthDialogOpen, settings, config, setAuthError, openAuthDialog]);

  const handleAuthSelect = useCallback(
    async (authType: AuthType | undefined, scope: SettingScope) => {
      if (authType) {
        await clearCachedCredentialFile();

        settings.setValue(scope, 'selectedAuthType', authType);

        if (isInitialSetup) {
          console.log(`Authentication method configured: ${authType}`);
          setIsInitialSetup(false);
        }

        if (
          authType === AuthType.LOGIN_WITH_GOOGLE &&
          config.isBrowserLaunchSuppressed()
        ) {
          runExitCleanup();
          console.log(
            `
----------------------------------------------------------------
Logging in with Google... Please restart Gemini CLI to continue.
----------------------------------------------------------------
            `,
          );
          process.exit(0);
        }
      }
      setIsAuthDialogOpen(false);
      setAuthError(null);
    },
    [settings, setAuthError, config, isInitialSetup],
  );

  const cancelAuthentication = useCallback(() => {
    setIsAuthenticating(false);
  }, []);

  return {
    isAuthDialogOpen,
    openAuthDialog,
    handleAuthSelect,
    isAuthenticating,
    cancelAuthentication,
  };
};
