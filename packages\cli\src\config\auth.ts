/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { AuthType } from '@google/gemini-cli-core';
import { loadEnvironment } from './settings.js';

export interface ProviderStatus {
  provider: string;
  authType: AuthType;
  available: boolean;
  hasApiKey: boolean;
  apiKeyValid: boolean;
  errorMessage?: string;
  displayName: string;
  description: string;
}

export interface ProviderInfo {
  name: string;
  authType: AuthType;
  envVar: string;
  displayName: string;
  description: string;
  testEndpoint?: string;
}

// Provider configurations
export const PROVIDER_CONFIGS: Record<string, ProviderInfo> = {
  google: {
    name: 'google',
    authType: AuthType.USE_GEMINI,
    envVar: 'GEMINI_API_KEY',
    displayName: 'Google Gemini',
    description: 'Google Gemini API with API key',
  },
  openai: {
    name: 'openai',
    authType: AuthType.USE_OPENAI,
    envVar: 'OPENAI_API_KEY',
    displayName: 'OpenAI',
    description: 'OpenAI GPT models',
    testEndpoint: 'https://api.openai.com/v1/models',
  },
  anthropic: {
    name: 'anthropic',
    authType: AuthType.USE_ANTHROPIC,
    envVar: 'ANTHROPIC_API_KEY',
    displayName: 'Anthropic Claude',
    description: 'Anthropic Claude models',
    testEndpoint: 'https://api.anthropic.com/v1/messages',
  },
  mistral: {
    name: 'mistral',
    authType: AuthType.USE_MISTRAL,
    envVar: 'MISTRAL_API_KEY',
    displayName: 'Mistral AI',
    description: 'Mistral AI models',
    testEndpoint: 'https://api.mistral.ai/v1/models',
  },
  openrouter: {
    name: 'openrouter',
    authType: AuthType.USE_OPENROUTER,
    envVar: 'OPENROUTER_API_KEY',
    displayName: 'OpenRouter',
    description: 'OpenRouter unified API',
    testEndpoint: 'https://openrouter.ai/api/v1/models',
  },
  custom: {
    name: 'custom',
    authType: AuthType.USE_CUSTOM,
    envVar: 'CUSTOM_API_KEY',
    displayName: 'Custom Provider',
    description: 'Custom OpenAI-compatible API',
  },
};

export const getAuthTypeFromProvider = (provider?: string): AuthType | undefined => {
  if (!provider) return undefined;

  const config = PROVIDER_CONFIGS[provider.toLowerCase()];
  return config?.authType;
};

/**
 * Checks if an API key is a placeholder value
 */
export const isPlaceholderApiKey = (apiKey: string): boolean => {
  if (!apiKey) return true;

  const placeholderPatterns = [
    /^your_.*_api_key_here$/i,
    /^sk-placeholder/i,
    /^placeholder/i,
    /^your.*key/i,
    /^api.*key.*here/i,
    /^replace.*with/i,
  ];

  return placeholderPatterns.some(pattern => pattern.test(apiKey.trim()));
};

/**
 * Validates if an API key has the correct format for a provider
 */
export const validateApiKeyFormat = (provider: string, apiKey: string): boolean => {
  if (!apiKey || isPlaceholderApiKey(apiKey)) return false;

  switch (provider.toLowerCase()) {
    case 'openai':
      return /^sk-[A-Za-z0-9]{48,}$/.test(apiKey);
    case 'anthropic':
      return /^sk-ant-[A-Za-z0-9-_]{95,}$/.test(apiKey);
    case 'mistral':
      return /^[A-Za-z0-9]{32,}$/.test(apiKey);
    case 'openrouter':
      return /^sk-or-v1-[A-Za-z0-9]{64,}$/.test(apiKey);
    case 'google':
      return /^[A-Za-z0-9_-]{39}$/.test(apiKey);
    case 'custom':
      return apiKey.length > 10; // Basic length check for custom providers
    default:
      return apiKey.length > 10;
  }
};

export const detectProviderFromEnvironment = (): string | undefined => {
  // Check environment variable first
  if (process.env.GEMINI_PROVIDER) {
    return process.env.GEMINI_PROVIDER;
  }

  // Auto-detect based on available and valid API keys
  for (const [providerName, config] of Object.entries(PROVIDER_CONFIGS)) {
    const apiKey = process.env[config.envVar];
    if (apiKey && !isPlaceholderApiKey(apiKey)) {
      return providerName;
    }
  }

  return undefined;
};

export const validateAuthMethod = (authMethod: string): string | null => {
  loadEnvironment();
  if (
    authMethod === AuthType.LOGIN_WITH_GOOGLE ||
    authMethod === AuthType.CLOUD_SHELL
  ) {
    return null;
  }

  if (authMethod === AuthType.USE_GEMINI) {
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      return 'GEMINI_API_KEY environment variable not found.\n' +
             '💡 Get your API key from https://aistudio.google.com/apikey\n' +
             '💡 Add it to your .env file: GEMINI_API_KEY=your_key_here';
    }
    if (isPlaceholderApiKey(apiKey)) {
      return 'GEMINI_API_KEY appears to be a placeholder value.\n' +
             '💡 Replace it with your actual API key from https://aistudio.google.com/apikey';
    }
    if (!validateApiKeyFormat('google', apiKey)) {
      return 'GEMINI_API_KEY has invalid format.\n' +
             '💡 Google API keys should be 39 characters long';
    }
    return null;
  }

  if (authMethod === AuthType.USE_VERTEX_AI) {
    const hasVertexProjectLocationConfig =
      !!process.env.GOOGLE_CLOUD_PROJECT && !!process.env.GOOGLE_CLOUD_LOCATION;
    const hasGoogleApiKey = !!process.env.GOOGLE_API_KEY;
    if (!hasVertexProjectLocationConfig && !hasGoogleApiKey) {
      return (
        'When using Vertex AI, you must specify either:\n' +
        '• GOOGLE_CLOUD_PROJECT and GOOGLE_CLOUD_LOCATION environment variables.\n' +
        '• GOOGLE_API_KEY environment variable (if using express mode).\n' +
        'Update your environment and try again (no reload needed if using .env)!'
      );
    }
    return null;
  }

  if (authMethod === AuthType.USE_OPENAI) {
    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey) {
      return 'OPENAI_API_KEY environment variable not found.\n' +
             '💡 Get your API key from https://platform.openai.com/api-keys\n' +
             '💡 Add it to your .env file: OPENAI_API_KEY=sk-...';
    }
    if (isPlaceholderApiKey(apiKey)) {
      return 'OPENAI_API_KEY appears to be a placeholder value.\n' +
             '💡 Replace it with your actual API key from https://platform.openai.com/api-keys';
    }
    if (!validateApiKeyFormat('openai', apiKey)) {
      return 'OPENAI_API_KEY has invalid format.\n' +
             '💡 OpenAI API keys should start with "sk-" and be at least 48 characters';
    }
    return null;
  }

  if (authMethod === AuthType.USE_ANTHROPIC) {
    const apiKey = process.env.ANTHROPIC_API_KEY;
    if (!apiKey) {
      return 'ANTHROPIC_API_KEY environment variable not found.\n' +
             '💡 Get your API key from https://console.anthropic.com/\n' +
             '💡 Add it to your .env file: ANTHROPIC_API_KEY=sk-ant-...';
    }
    if (isPlaceholderApiKey(apiKey)) {
      return 'ANTHROPIC_API_KEY appears to be a placeholder value.\n' +
             '💡 Replace it with your actual API key from https://console.anthropic.com/';
    }
    if (!validateApiKeyFormat('anthropic', apiKey)) {
      return 'ANTHROPIC_API_KEY has invalid format.\n' +
             '💡 Anthropic API keys should start with "sk-ant-"';
    }
    return null;
  }

  if (authMethod === AuthType.USE_MISTRAL) {
    const apiKey = process.env.MISTRAL_API_KEY;
    if (!apiKey) {
      return 'MISTRAL_API_KEY environment variable not found.\n' +
             '💡 Get your API key from https://console.mistral.ai/\n' +
             '💡 Add it to your .env file: MISTRAL_API_KEY=your_key_here';
    }
    if (isPlaceholderApiKey(apiKey)) {
      return 'MISTRAL_API_KEY appears to be a placeholder value.\n' +
             '💡 Replace it with your actual API key from https://console.mistral.ai/';
    }
    if (!validateApiKeyFormat('mistral', apiKey)) {
      return 'MISTRAL_API_KEY has invalid format.\n' +
             '💡 Mistral API keys should be at least 32 characters long';
    }
    return null;
  }

  if (authMethod === AuthType.USE_OPENROUTER) {
    const apiKey = process.env.OPENROUTER_API_KEY;
    if (!apiKey) {
      return 'OPENROUTER_API_KEY environment variable not found.\n' +
             '💡 Get your API key from https://openrouter.ai/keys\n' +
             '💡 Add it to your .env file: OPENROUTER_API_KEY=sk-or-v1-...';
    }
    if (isPlaceholderApiKey(apiKey)) {
      return 'OPENROUTER_API_KEY appears to be a placeholder value.\n' +
             '💡 Replace it with your actual API key from https://openrouter.ai/keys';
    }
    if (!validateApiKeyFormat('openrouter', apiKey)) {
      return 'OPENROUTER_API_KEY has invalid format.\n' +
             '💡 OpenRouter API keys should start with "sk-or-v1-"';
    }
    return null;
  }

  if (authMethod === AuthType.USE_CUSTOM) {
    const apiKey = process.env.CUSTOM_API_KEY;
    if (!apiKey) {
      return 'CUSTOM_API_KEY environment variable not found.\n' +
             '💡 Add your custom provider API key to .env file: CUSTOM_API_KEY=your_key_here\n' +
             '💡 Also set CUSTOM_BASE_URL if needed';
    }
    if (isPlaceholderApiKey(apiKey)) {
      return 'CUSTOM_API_KEY appears to be a placeholder value.\n' +
             '💡 Replace it with your actual API key';
    }
    return null;
  }

  return 'Invalid auth method selected.';
};

/**
 * Gets the status of all available providers
 */
export const getAllProviderStatuses = async (): Promise<ProviderStatus[]> => {
  loadEnvironment();

  const statuses: ProviderStatus[] = [];

  for (const [providerName, config] of Object.entries(PROVIDER_CONFIGS)) {
    const apiKey = process.env[config.envVar];
    const hasApiKey = !!apiKey && !isPlaceholderApiKey(apiKey);
    const apiKeyValid = hasApiKey ? validateApiKeyFormat(providerName, apiKey) : false;

    let errorMessage: string | undefined;
    if (!hasApiKey) {
      errorMessage = `${config.envVar} environment variable not found or is placeholder`;
    } else if (!apiKeyValid) {
      errorMessage = `${config.envVar} has invalid format`;
    }

    statuses.push({
      provider: providerName,
      authType: config.authType,
      available: hasApiKey && apiKeyValid,
      hasApiKey,
      apiKeyValid,
      errorMessage,
      displayName: config.displayName,
      description: config.description,
    });
  }

  // Add Google OAuth option
  statuses.unshift({
    provider: 'google-oauth',
    authType: AuthType.LOGIN_WITH_GOOGLE,
    available: true,
    hasApiKey: true,
    apiKeyValid: true,
    displayName: 'Google OAuth',
    description: 'Login with Google account (OAuth)',
  });

  // Add Vertex AI option if in Google Cloud environment
  if (process.env.GOOGLE_CLOUD_PROJECT || process.env.CLOUD_SHELL === 'true') {
    statuses.push({
      provider: 'vertex-ai',
      authType: AuthType.USE_VERTEX_AI,
      available: true,
      hasApiKey: true,
      apiKeyValid: true,
      displayName: 'Vertex AI',
      description: 'Google Cloud Vertex AI',
    });
  }

  return statuses;
};

/**
 * Gets available providers (those with valid API keys)
 */
export const getAvailableProviders = async (): Promise<ProviderStatus[]> => {
  const allStatuses = await getAllProviderStatuses();
  return allStatuses.filter(status => status.available);
};

/**
 * Suggests the best provider based on available API keys and .env configuration
 */
export const suggestBestProvider = async (): Promise<string | undefined> => {
  // First check .env GEMINI_PROVIDER setting
  if (process.env.GEMINI_PROVIDER) {
    const envProvider = process.env.GEMINI_PROVIDER.toLowerCase();
    const config = PROVIDER_CONFIGS[envProvider];
    if (config) {
      const apiKey = process.env[config.envVar];
      if (apiKey && !isPlaceholderApiKey(apiKey) && validateApiKeyFormat(envProvider, apiKey)) {
        return envProvider;
      }
    }
  }

  // Then check for any available provider
  const availableProviders = await getAvailableProviders();
  const nonOAuthProviders = availableProviders.filter(p =>
    p.authType !== AuthType.LOGIN_WITH_GOOGLE &&
    p.authType !== AuthType.USE_VERTEX_AI
  );

  if (nonOAuthProviders.length > 0) {
    return nonOAuthProviders[0].provider;
  }

  return undefined;
};

/**
 * Gets alternative provider suggestions when current provider fails
 */
export const getAlternativeProviderSuggestions = async (failedProvider: string): Promise<string[]> => {
  const availableProviders = await getAvailableProviders();
  return availableProviders
    .filter(p => p.provider !== failedProvider)
    .map(p => p.displayName);
};

/**
 * Generates helpful error message with suggestions
 */
export const generateAuthErrorMessage = async (error: string, authType?: AuthType): Promise<string> => {
  let message = error;

  if (authType) {
    const failedProvider = Object.entries(PROVIDER_CONFIGS)
      .find(([_, config]) => config.authType === authType)?.[0];

    if (failedProvider) {
      const alternatives = await getAlternativeProviderSuggestions(failedProvider);
      if (alternatives.length > 0) {
        message += `\n\n🔄 Alternative providers available: ${alternatives.join(', ')}`;
        message += '\n💡 Use /auth command to switch providers';
      }
    }
  }

  return message;
};
